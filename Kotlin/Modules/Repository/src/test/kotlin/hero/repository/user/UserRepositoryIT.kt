package hero.repository.user

import hero.baseutils.truncated
import hero.exceptions.http.NotFoundException
import hero.jackson.fromJson
import hero.model.Analytics
import hero.model.Creator
import hero.model.DiscordMeta
import hero.model.GjirafaLivestreamMeta
import hero.model.ImageAsset
import hero.model.NotificationsEnabled
import hero.model.Role
import hero.model.SpotifyMeta
import hero.model.SupportCounts
import hero.model.UserCompany
import hero.model.UserStatus
import hero.repository.RepositoryTest
import hero.repository.user
import hero.sql.jooq.Tables.NOTIFICATION_SETTINGS
import hero.sql.jooq.Tables.USER
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class UserRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should export user object to postgres`() {
            val underTest = UserRepository(testContext)

            val user = user(
                id = "cestmir",
                email = "<EMAIL>",
                emailVerified = true,
                bio = "cestmir bio",
                bioHtml = "cestmir bio html",
                bioEn = "cestmir bio en",
                bioHtmEn = "cestmir bio html en",
                path = "cestmir-path",
                role = Role.MODERATOR,
                language = "en",
                status = UserStatus.ACTIVE,
                firebaseId = "firebaseId",
                facebookId = "facebookId",
                googleId = "googleId",
                airtableId = "airtableId",
                isExplicit = false,
                isFeatured = false,
                featuredLanguages = listOf("en", "cs"),
                verifiedAt = Instant.ofEpochSecond(1742070269),
                isOfAge = false,
                hasLivestream = false,
                hasSpotifyExport = false,
                hasDrm = false,
                hasRssFeed = false,
                image = ImageAsset("https://image.com", 500, 300),
                counts = SupportCounts(100, 20, 50, 300, 270, 50, 60, 70, 35),
                customerIds = mutableMapOf("eur" to "cus_hwjqkq"),
                discord = DiscordMeta("discord-id", "guild-id", "role-id"),
                spotify = SpotifyMeta("id", "accessToken", "refreshToken", Instant.now().truncated()),
                gjirafaLivestream = GjirafaLivestreamMeta("public-id", "stream-url", "stream-key", ""),
                notificationsEnabled = NotificationsEnabled(
                    emailNewPost = false,
                    emailNewDm = false,
                    pushNewComment = false,
                    pushNewPost = false,
                    newsletter = false,
                    termsChanged = false,
                ),
                canCreateCommunity = true,
            )

            underTest.save(user)

            val userResults = testContext
                .select(USER.asterisk())
                .from(USER)
                .fetch()

            assertThat(userResults).hasSize(1)
            val exportedUser = userResults[0]

            assertThat(exportedUser[USER.ID]).isEqualTo("cestmir")
            assertThat(exportedUser[USER.NAME]).isEqualTo(user.name)
            assertThat(exportedUser[USER.EMAIL]).isEqualTo(user.email)
            assertThat(exportedUser[USER.EMAIL_VERIFIED]).isTrue()

            assertThat(exportedUser[USER.BIO]).isEqualTo("cestmir bio")
            assertThat(exportedUser[USER.BIO_HTML]).isEqualTo("cestmir bio html")
            assertThat(exportedUser[USER.BIO_EN]).isEqualTo("cestmir bio en")
            assertThat(exportedUser[USER.BIO_HTML_EN]).isEqualTo("cestmir bio html en")

            assertThat(exportedUser[USER.PATH]).isEqualTo("cestmir-path")
            assertThat(exportedUser[USER.ROLE]).isEqualTo("MODERATOR")

            assertThat(exportedUser[USER.LANGUAGE]).isEqualTo("en")
            assertThat(exportedUser[USER.STATUS]).isEqualTo("ACTIVE")

            assertThat(exportedUser[USER.FIREBASE_ID]).isEqualTo("firebaseId")
            assertThat(exportedUser[USER.FACEBOOK_ID]).isEqualTo("facebookId")
            assertThat(exportedUser[USER.GOOGLE_ID]).isEqualTo("googleId")
            assertThat(exportedUser[USER.AIRTABLE_ID]).isEqualTo("airtableId")

            assertThat(exportedUser[USER.IS_EXPLICIT]).isFalse()
            assertThat(exportedUser[USER.IS_FEATURED]).isFalse()
            assertThat(exportedUser[USER.FEATURED_IN_LANGUAGES]).isEqualTo(arrayOf("en", "cs"))
            assertThat(exportedUser[USER.HAS_LIVESTREAMS]).isFalse()
            assertThat(exportedUser[USER.HAS_SPOTIFY_EXPORT]).isFalse()
            assertThat(exportedUser[USER.HAS_DRM]).isFalse()
            assertThat(exportedUser[USER.HAS_RSS_FEED]).isFalse()

            assertThat(exportedUser[USER.PROFILE_IMAGE_URL]).isEqualTo("https://image.com")
            assertThat(exportedUser[USER.PROFILE_IMAGE_WIDTH]).isEqualTo(500)
            assertThat(exportedUser[USER.PROFILE_IMAGE_HEIGHT]).isEqualTo(300)

            assertThat(exportedUser[USER.SUBSCRIBERS_COUNT]).isEqualTo(100)
            assertThat(exportedUser[USER.SUBSCRIBERS_COUNT_THRESHOLD]).isEqualTo(50)
            assertThat(exportedUser[USER.SUBSCRIPTIONS_COUNT]).isEqualTo(20)
            assertThat(exportedUser[USER.INCOME_RAW]).isEqualTo(300)
            assertThat(exportedUser[USER.INCOME_CLEAN]).isEqualTo(270)
            assertThat(exportedUser[USER.INVOICES_COUNT]).isEqualTo(70)
            assertThat(exportedUser[USER.POSTS_COUNT]).isEqualTo(60)
            assertThat(exportedUser[USER.PAYMENTS]).isEqualTo(50)
            assertThat(exportedUser[USER.PENDING_REQUESTS_COUNT]).isEqualTo(35)

            assertThat(
                exportedUser[USER.CUSTOMER_IDS].data().fromJson<Map<String, String>>(),
            ).isEqualTo(user.customerIds)
            assertThat(exportedUser[USER.DISCORD].data().fromJson<DiscordMeta>()).isEqualTo(user.discord)
            assertThat(exportedUser[USER.SPOTIFY].data().fromJson<SpotifyMeta>()).isEqualTo(user.spotify)
            assertThat(exportedUser[USER.SPOTIFY_URI]).isEqualTo(user.spotifyUri)
            assertThat(exportedUser[USER.SPOTIFY_FEED_READY]).isEqualTo(user.spotifyFeedReady)
            assertThat(
                exportedUser[USER.GJIRAFA_LIVESTREAM].data().fromJson<GjirafaLivestreamMeta>(),
            ).isEqualTo(user.gjirafaLivestream)
            assertThat(exportedUser[USER.ANALYTICS].data().fromJson<Analytics>()).isEqualTo(user.analytics)

            assertThat(exportedUser[USER.CREATOR].data().fromJson<Creator>()).isEqualTo(user.creator)
            assertThat(exportedUser[USER.COMPANY].data().fromJson<UserCompany>()).isEqualTo(user.company)

            assertThat(exportedUser[USER.CREATED_AT]).isEqualTo(user.created)
            assertThat(exportedUser[USER.DELETED_AT]).isEqualTo(user.deletedAt)
            assertThat(exportedUser[USER.PATH_CHANGED_AT]).isEqualTo(user.pathChanged)
            assertThat(exportedUser[USER.ONE_STOP_SHOP_AT]).isEqualTo(user.oneStopShopAt)
            assertThat(exportedUser[USER.PRIVACY_POLICY_EFFECTIVE_AT]).isEqualTo(user.privacyPolicyEffectiveAt)
            assertThat(exportedUser[USER.LAST_CHARGE_FAILED_AT]).isEqualTo(user.lastChargeFailedAt)
            assertThat(exportedUser[USER.LAST_POST_AT]).isEqualTo(user.lastPostAt)
            assertThat(exportedUser[USER.VERIFIED_AT]).isEqualTo(Instant.ofEpochSecond(1742070269))

            assertThat(exportedUser[USER.IS_OF_AGE]).isFalse()
            assertThat(exportedUser[USER.MODERATOR_PERMISSIONS]).isEqualTo(0)
            assertThat(exportedUser[USER.CAN_CREATE_COMMUNITY]).isTrue()

            val notificationSettingsResults = testContext
                .select(NOTIFICATION_SETTINGS.asterisk())
                .from(NOTIFICATION_SETTINGS)
                .fetch()

            assertThat(notificationSettingsResults).hasSize(1)
            val expectedNotificationSettings = notificationSettingsResults[0]

            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.USER_ID]).isEqualTo("cestmir")

            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.EMAIL_NEW_POST]).isFalse()
            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.EMAIL_NEW_DM]).isFalse()
            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.PUSH_NEW_POST]).isFalse()
            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.PUSH_NEW_COMMENT]).isFalse()
            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.NEWSLETTER]).isFalse()
            assertThat(expectedNotificationSettings[NOTIFICATION_SETTINGS.TERMS_CHANGED]).isFalse()
        }
    }

    @Nested
    inner class Find {
        @Test
        fun `should find all users in database`() {
            val underTest = UserRepository(testContext)

            val expectedUser = user(emailVerified = true)
            createUser(expectedUser)

            val results = underTest.find { this }
            assertThat(results).containsExactly(expectedUser)
        }
    }

    @Nested
    inner class FindSingle {
        @Test
        fun `should find single user with given path`() {
            val underTest = UserRepository(testContext)

            val expectedUser = user("cestmir", path = "cestik")
            createUser(expectedUser)
            createUser(user("erem", path = "eremik"))

            val result = underTest.findSingle { where(USER.PATH.eq("cestik")) }
            assertThat(result).isEqualTo(expectedUser)
        }

        @Test
        fun `should not find any user with given path and should return null`() {
            val underTest = UserRepository(testContext)

            createUser(user("erem", path = "eremik"))

            val result = underTest.findSingle { where(USER.PATH.eq("cestik")) }
            assertThat(result).isNull()
        }
    }

    @Nested
    inner class GetById {
        @Test
        fun `should get given user by id`() {
            val underTest = UserRepository(testContext)

            val expectedUser = user("cestmir")
            createUser(expectedUser)

            val result = underTest.getById("cestmir")
            assertThat(result).isEqualTo(expectedUser)
        }

        @Test
        fun `should throw if user with given id does not exist`() {
            val underTest = UserRepository(testContext)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.getById("cestmir")
            }
        }
    }

    @Nested
    inner class FindById {
        @Test
        fun `should find given user by id`() {
            val underTest = UserRepository(testContext)

            val expectedUser = user("cestmir")
            createUser(expectedUser)

            val result = underTest.findById("cestmir")
            assertThat(result).isEqualTo(expectedUser)
        }

        @Test
        fun `should return null if user with given id does not exist`() {
            val underTest = UserRepository(testContext)

            val result = underTest.findById("cestmir")

            assertThat(result).isNull()
        }
    }
}
