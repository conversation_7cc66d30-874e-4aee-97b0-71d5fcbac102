Kotlin/Function/CommunityCreatedHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/SQL/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/CommunityCreatedHandler/variables:
  variables:
    FUNCTION_NAME: "community-created-handler"
    CLASS_NAME: "hero.functions.CommunityCreatedHandler"
    TOPIC: "CommunityCreated"

Kotlin/Function/CommunityCreatedHandler/deploy-devel:
  needs:
    - Kotlin/Function/CommunityCreatedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/CommunityCreatedHandler/variables

Kotlin/Function/CommunityCreatedHandler/deploy-prod:
  needs:
    - Kotlin/Function/CommunityCreatedHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/CommunityCreatedHandler/variables
