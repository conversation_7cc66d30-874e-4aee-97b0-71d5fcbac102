package hero.functions

import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.model.CommunityMemberStatus
import hero.model.topics.CommunityCreated
import hero.repository.community.CommunityRepository
import hero.repository.subscription.JooqSubscriptionHelper
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import org.jooq.impl.DSL
import java.time.Instant

@Suppress("Unused")
class CommunityCreatedHandler(
    lazyContext: Lazy<DSLContext> = lazy { JooqSQL.context(ConnectorConnectionPool.dataSource) },
    systemEnv: EnvironmentVariables = SystemEnv,
    private val communityRepository: CommunityRepository = CommunityRepository(lazyContext),
) : PubSubSubscriber<CommunityCreated>(systemEnv) {
    private val context: DSLContext by lazyContext

    override fun consume(payload: CommunityCreated) {
        val community = communityRepository.getById(payload.communityId)

        context.deleteFrom(Tables.COMMUNITY_MEMBER)
            .where(Tables.COMMUNITY_MEMBER.COMMUNITY_ID.eq(community.id))
            .execute()

        val now = Instant.now()
        context.insertInto(Tables.COMMUNITY_MEMBER)
            .columns(
                Tables.COMMUNITY_MEMBER.COMMUNITY_ID,
                Tables.COMMUNITY_MEMBER.USER_ID,
                Tables.COMMUNITY_MEMBER.JOINED_AT,
                Tables.COMMUNITY_MEMBER.UPDATED_AT,
                Tables.COMMUNITY_MEMBER.STATE,
            )
            .select(
                DSL
                    .select(
                        DSL.inline(community.id),
                        // TODO create constraint on subscription table
                        Tables.SUBSCRIPTION.USER_ID,
                        DSL.inline(now),
                        DSL.inline(now),
                        DSL.inline(CommunityMemberStatus.ACTIVE.name),
                    )
                    .from(Tables.SUBSCRIPTION)
                    .where(Tables.SUBSCRIPTION.CREATOR_ID.eq(community.ownerId))
                    .and(JooqSubscriptionHelper.activeSubscription),
            )
            .execute()
    }
}
