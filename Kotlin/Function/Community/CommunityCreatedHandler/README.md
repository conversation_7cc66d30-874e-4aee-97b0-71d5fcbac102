# Community<PERSON>reate<PERSON><PERSON><PERSON>ler

A Google Cloud Function that handles the `CommunityCreated` pub/sub event to automatically add existing subscribers to newly created communities.

## Overview

When a creator creates a new community, this handler automatically adds all users who are currently subscribed to that creator as active members of the new community. This ensures that existing supporters are immediately part of the creator's community without requiring manual invitation or joining.

## Functionality

### Event Processing
- **Trigger**: Listens for `CommunityCreated` pub/sub events
- **Event Payload**: `CommunityCreated(communityId: UUID)`
- **Publisher**: `CommunityCommandService.execute(CreateCommunity)` publishes this event when a community is successfully created

### Core Logic
1. **Retrieve Community**: Fetches the community details using the provided `communityId`
2. **Clean Existing Members**: Removes any existing community members (defensive cleanup)
3. **Add Subscribers**: Automatically adds all active subscribers of the community owner as `ACTIVE` community members

### Database Operations
- **Cleanup**: `DELETE FROM community_member WHERE community_id = ?`
- **Bulk Insert**: Inserts all active subscribers as community members with:
  - `community_id`: The newly created community ID
  - `user_id`: Each subscriber's user ID
  - `joined_at`: Current timestamp
  - `updated_at`: Current timestamp
  - `state`: `ACTIVE`

## Dependencies

### Required Modules
- `:Function:Subscriber` - Base pub/sub subscriber functionality
- `:Modules:SQL` - Database access and JOOQ integration
- `:Modules:IntegrationTesting` - Testing utilities

### Key Dependencies
- `CommunityRepository` - For fetching community details
- `JooqSQL` - Database context and operations
- `Tables.COMMUNITY_MEMBER` - Community membership table
- `Tables.SUBSCRIPTION` - User subscription data
- `JooqSubscriptionHelper.activeSubscription` - Filter for active subscriptions

## Configuration

### Environment Variables
Inherits standard pub/sub subscriber environment variables through `SystemEnv`.

### Cloud Function Settings
- **Function Name**: `community-created-handler`
- **Class Name**: `hero.functions.CommunityCreatedHandler`
- **Topic**: `CommunityCreated`
- **Trigger**: Pub/Sub message

## Usage Flow

1. **Community Creation**: User creates a community via API
2. **Event Publishing**: `CommunityCommandService` publishes `CommunityCreated` event
3. **Handler Execution**: This function processes the event
4. **Member Addition**: All active subscribers become community members
5. **Result**: Community is populated with existing supporters

## Testing

### Integration Test
The `CommunityCreatedHandlerIT` test verifies:
- Handler correctly processes `CommunityCreated` events
- Subscribers are added as active community members
- Database state is properly updated

### Test Scenario
```kotlin
// Given: A creator with subscribers
testHelper.createUser("creator")
val community = testHelper.createCommunity("creator")
testHelper.createSubscription(userId = "subscriber", creatorId = "creator")

// When: CommunityCreated event is processed
handler.consume(CommunityCreated(community.id))

// Then: Subscriber becomes community member
assertThat(communityMembers).hasSize(1)
assertThat(communityMembers.first().userId).isEqualTo("subscriber")
assertThat(communityMembers.first().state).isEqualTo("ACTIVE")
```

## Implementation Details

### SQL Query Logic
The handler uses a single `INSERT INTO ... SELECT` statement to efficiently:
1. Select all active subscribers of the community owner
2. Insert them as community members with proper timestamps and status
3. Handle the operation atomically

### Error Handling
- Inherits error handling from `PubSubSubscriber` base class
- Database operations are transactional
- Failed processing will be retried according to pub/sub retry policy

### Performance Considerations
- Uses bulk insert for efficiency when adding multiple subscribers
- Defensive cleanup ensures no duplicate memberships
- Single database transaction for consistency

## Related Components

- **CommunityCommandService**: Publishes the triggering event
- **CommunityRepository**: Provides community data access
- **Community Member Table**: Stores membership relationships
- **Subscription Table**: Source of subscriber data

## Deployment

Deployed as a Google Cloud Function with:
- Automatic scaling based on pub/sub message volume
- Environment-specific configuration (devel/production)
- Integration with existing pub/sub infrastructure
