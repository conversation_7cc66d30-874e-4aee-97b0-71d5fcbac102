# CommunityCreatedHandler

A Google Cloud Function that handles the `CommunityCreated` pub/sub event to automatically add existing subscribers to newly created communities. When a creator creates a new community, this handler fetches all users who are currently subscribed to that creator and adds them as active members of the new community. The handler first cleans any existing community members, then performs a bulk insert of all active subscribers with `ACTIVE` status. This ensures that existing supporters are immediately part of the creator's community without requiring manual invitation. The function is deployed as `community-created-handler` and listens to the `CommunityCreated` topic.
