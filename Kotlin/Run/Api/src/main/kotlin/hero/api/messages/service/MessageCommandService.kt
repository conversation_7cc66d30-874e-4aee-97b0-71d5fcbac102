package hero.api.messages.service

import hero.api.post.service.CreatePost
import hero.api.post.service.PostService
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.model.MessageThread
import hero.model.Post
import java.time.Clock
import java.time.Instant

class MessageCommandService(
    // todo replace with repository
    private val messageThreadsCollection: TypedCollectionReference<MessageThread>,
    private val postService: PostService,
    private val clock: Clock = Clock.systemUTC(),
) {
    fun execute(command: SendMessage): Post {
        val messageThread = messageThreadsCollection[command.messageThreadId].get()

        if (command.userId !in messageThread.userIds) {
            throw ForbiddenException(
                "User ${command.userId} is not part of the message thread ${command.messageThreadId}",
            )
        }

        if (!messageThread.canMessage.getValue(command.userId)) {
            throw ForbiddenException(
                "User ${command.userId} cannot send message to the message thread ${command.messageThreadId}",
            )
        }

        val message = postService.execute(
            CreatePost(
                userId = command.userId,
                messageThreadId = command.messageThreadId,
                publishedAt = Instant.now(clock),
                text = command.text,
                textHtml = null,
                textDelta = null,
                assets = listOf(),
                isSponsored = false,
                isAgeRestricted = false,
            ),
        )

        // TODO there is an issue with data race here, we can fix this with locking only
        // a message can come, which will take a while to be processed for unknown reasons, second message comes
        // updates the message thread, but then first message is processed and updates the message thread to old data
        messageThreadsCollection[messageThread.id].set(
            messageThread.copy(
                lastMessageAt = message.published,
                lastMessageBy = message.userId,
                lastMessageId = message.id,
                seens = messageThread.seens.plus(command.userId to message.published),
                checks = messageThread.checks.plus(command.userId to message.published),
            ),
        )

        return message
    }
}

data class SendMessage(
    val userId: String,
    val messageThreadId: String,
    val text: String,
)
